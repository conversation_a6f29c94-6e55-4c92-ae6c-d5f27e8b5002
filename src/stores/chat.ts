import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { chatAPI, type ChatMessage } from '@/services/api';

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref<ChatMessage[]>([]);
  const currentThreadId = ref<string>('');
  const isLoading = ref(false);
  const isConnected = ref(false);
  const websocket = ref<WebSocket | null>(null);

  // 计算属性
  const hasMessages = computed(() => messages.value.length > 0);
  const lastMessage = computed(() => 
    messages.value[messages.value.length - 1]
  );

  // 方法
  const generateThreadId = () => {
    return `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const addMessage = (message: ChatMessage) => {
    messages.value.push({
      ...message,
      timestamp: message.timestamp || new Date().toISOString(),
    });
  };

  const sendMessage = async (content: string) => {
    if (!content.trim()) return;

    // 确保有线程ID
    if (!currentThreadId.value) {
      currentThreadId.value = generateThreadId();
    }

    // 添加用户消息
    addMessage({
      role: 'user',
      content: content.trim(),
    });

    isLoading.value = true;

    try {
      // 发送到后端
      const response = await chatAPI.sendMessage({
        message: content.trim(),
        thread_id: currentThreadId.value,
      });

      // 添加AI回复
      addMessage({
        role: 'assistant',
        content: response.message,
      });

    } catch (error) {
      console.error('发送消息失败:', error);
      addMessage({
        role: 'assistant',
        content: '抱歉，发送消息时出现错误，请重试。',
      });
    } finally {
      isLoading.value = false;
    }
  };

  const connectWebSocket = () => {
    if (!currentThreadId.value) {
      currentThreadId.value = generateThreadId();
    }

    try {
      websocket.value = chatAPI.connectWebSocket(currentThreadId.value);
      
      websocket.value.onopen = () => {
        isConnected.value = true;
        console.log('WebSocket连接已建立');
      };

      websocket.value.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'ai_chunk') {
          // 处理流式响应
          const lastMsg = messages.value[messages.value.length - 1];
          if (lastMsg && lastMsg.role === 'assistant' && lastMsg.content === '正在思考中...') {
            lastMsg.content = data.chunk.response || data.chunk;
          } else {
            addMessage({
              role: 'assistant',
              content: data.chunk.response || data.chunk,
            });
          }
        }
      };

      websocket.value.onclose = () => {
        isConnected.value = false;
        console.log('WebSocket连接已关闭');
      };

      websocket.value.onerror = (error) => {
        console.error('WebSocket错误:', error);
        isConnected.value = false;
      };

    } catch (error) {
      console.error('WebSocket连接失败:', error);
    }
  };

  const disconnectWebSocket = () => {
    if (websocket.value) {
      websocket.value.close();
      websocket.value = null;
      isConnected.value = false;
    }
  };

  const clearMessages = () => {
    messages.value = [];
    currentThreadId.value = '';
  };

  const loadSessionHistory = async (threadId: string) => {
    try {
      const history = await chatAPI.getSessionHistory(threadId);
      messages.value = history;
      currentThreadId.value = threadId;
    } catch (error) {
      console.error('加载会话历史失败:', error);
    }
  };

  return {
    // 状态
    messages,
    currentThreadId,
    isLoading,
    isConnected,
    
    // 计算属性
    hasMessages,
    lastMessage,
    
    // 方法
    addMessage,
    sendMessage,
    connectWebSocket,
    disconnectWebSocket,
    clearMessages,
    loadSessionHistory,
  };
});
