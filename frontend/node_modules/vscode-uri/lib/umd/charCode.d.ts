/**
 * An inlined enum containing useful character codes (to be used with String.charCodeAt).
 * Please leave the const keyword such that it gets inlined when compiled to JavaScript!
 */
export declare const enum CharCode {
    Null = 0,
    /**
     * The `\b` character.
     */
    Backspace = 8,
    /**
     * The `\t` character.
     */
    Tab = 9,
    /**
     * The `\n` character.
     */
    LineFeed = 10,
    /**
     * The `\r` character.
     */
    CarriageReturn = 13,
    Space = 32,
    /**
     * The `!` character.
     */
    ExclamationMark = 33,
    /**
     * The `"` character.
     */
    DoubleQuote = 34,
    /**
     * The `#` character.
     */
    Hash = 35,
    /**
     * The `$` character.
     */
    DollarSign = 36,
    /**
     * The `%` character.
     */
    PercentSign = 37,
    /**
     * The `&` character.
     */
    Ampersand = 38,
    /**
     * The `'` character.
     */
    SingleQuote = 39,
    /**
     * The `(` character.
     */
    OpenParen = 40,
    /**
     * The `)` character.
     */
    CloseParen = 41,
    /**
     * The `*` character.
     */
    Asterisk = 42,
    /**
     * The `+` character.
     */
    Plus = 43,
    /**
     * The `,` character.
     */
    Comma = 44,
    /**
     * The `-` character.
     */
    Dash = 45,
    /**
     * The `.` character.
     */
    Period = 46,
    /**
     * The `/` character.
     */
    Slash = 47,
    Digit0 = 48,
    Digit1 = 49,
    Digit2 = 50,
    Digit3 = 51,
    Digit4 = 52,
    Digit5 = 53,
    Digit6 = 54,
    Digit7 = 55,
    Digit8 = 56,
    Digit9 = 57,
    /**
     * The `:` character.
     */
    Colon = 58,
    /**
     * The `;` character.
     */
    Semicolon = 59,
    /**
     * The `<` character.
     */
    LessThan = 60,
    /**
     * The `=` character.
     */
    Equals = 61,
    /**
     * The `>` character.
     */
    GreaterThan = 62,
    /**
     * The `?` character.
     */
    QuestionMark = 63,
    /**
     * The `@` character.
     */
    AtSign = 64,
    A = 65,
    B = 66,
    C = 67,
    D = 68,
    E = 69,
    F = 70,
    G = 71,
    H = 72,
    I = 73,
    J = 74,
    K = 75,
    L = 76,
    M = 77,
    N = 78,
    O = 79,
    P = 80,
    Q = 81,
    R = 82,
    S = 83,
    T = 84,
    U = 85,
    V = 86,
    W = 87,
    X = 88,
    Y = 89,
    Z = 90,
    /**
     * The `[` character.
     */
    OpenSquareBracket = 91,
    /**
     * The `\` character.
     */
    Backslash = 92,
    /**
     * The `]` character.
     */
    CloseSquareBracket = 93,
    /**
     * The `^` character.
     */
    Caret = 94,
    /**
     * The `_` character.
     */
    Underline = 95,
    /**
     * The ``(`)`` character.
     */
    BackTick = 96,
    a = 97,
    b = 98,
    c = 99,
    d = 100,
    e = 101,
    f = 102,
    g = 103,
    h = 104,
    i = 105,
    j = 106,
    k = 107,
    l = 108,
    m = 109,
    n = 110,
    o = 111,
    p = 112,
    q = 113,
    r = 114,
    s = 115,
    t = 116,
    u = 117,
    v = 118,
    w = 119,
    x = 120,
    y = 121,
    z = 122,
    /**
     * The `{` character.
     */
    OpenCurlyBrace = 123,
    /**
     * The `|` character.
     */
    Pipe = 124,
    /**
     * The `}` character.
     */
    CloseCurlyBrace = 125,
    /**
     * The `~` character.
     */
    Tilde = 126,
    U_Combining_Grave_Accent = 768,
    U_Combining_Acute_Accent = 769,
    U_Combining_Circumflex_Accent = 770,
    U_Combining_Tilde = 771,
    U_Combining_Macron = 772,
    U_Combining_Overline = 773,
    U_Combining_Breve = 774,
    U_Combining_Dot_Above = 775,
    U_Combining_Diaeresis = 776,
    U_Combining_Hook_Above = 777,
    U_Combining_Ring_Above = 778,
    U_Combining_Double_Acute_Accent = 779,
    U_Combining_Caron = 780,
    U_Combining_Vertical_Line_Above = 781,
    U_Combining_Double_Vertical_Line_Above = 782,
    U_Combining_Double_Grave_Accent = 783,
    U_Combining_Candrabindu = 784,
    U_Combining_Inverted_Breve = 785,
    U_Combining_Turned_Comma_Above = 786,
    U_Combining_Comma_Above = 787,
    U_Combining_Reversed_Comma_Above = 788,
    U_Combining_Comma_Above_Right = 789,
    U_Combining_Grave_Accent_Below = 790,
    U_Combining_Acute_Accent_Below = 791,
    U_Combining_Left_Tack_Below = 792,
    U_Combining_Right_Tack_Below = 793,
    U_Combining_Left_Angle_Above = 794,
    U_Combining_Horn = 795,
    U_Combining_Left_Half_Ring_Below = 796,
    U_Combining_Up_Tack_Below = 797,
    U_Combining_Down_Tack_Below = 798,
    U_Combining_Plus_Sign_Below = 799,
    U_Combining_Minus_Sign_Below = 800,
    U_Combining_Palatalized_Hook_Below = 801,
    U_Combining_Retroflex_Hook_Below = 802,
    U_Combining_Dot_Below = 803,
    U_Combining_Diaeresis_Below = 804,
    U_Combining_Ring_Below = 805,
    U_Combining_Comma_Below = 806,
    U_Combining_Cedilla = 807,
    U_Combining_Ogonek = 808,
    U_Combining_Vertical_Line_Below = 809,
    U_Combining_Bridge_Below = 810,
    U_Combining_Inverted_Double_Arch_Below = 811,
    U_Combining_Caron_Below = 812,
    U_Combining_Circumflex_Accent_Below = 813,
    U_Combining_Breve_Below = 814,
    U_Combining_Inverted_Breve_Below = 815,
    U_Combining_Tilde_Below = 816,
    U_Combining_Macron_Below = 817,
    U_Combining_Low_Line = 818,
    U_Combining_Double_Low_Line = 819,
    U_Combining_Tilde_Overlay = 820,
    U_Combining_Short_Stroke_Overlay = 821,
    U_Combining_Long_Stroke_Overlay = 822,
    U_Combining_Short_Solidus_Overlay = 823,
    U_Combining_Long_Solidus_Overlay = 824,
    U_Combining_Right_Half_Ring_Below = 825,
    U_Combining_Inverted_Bridge_Below = 826,
    U_Combining_Square_Below = 827,
    U_Combining_Seagull_Below = 828,
    U_Combining_X_Above = 829,
    U_Combining_Vertical_Tilde = 830,
    U_Combining_Double_Overline = 831,
    U_Combining_Grave_Tone_Mark = 832,
    U_Combining_Acute_Tone_Mark = 833,
    U_Combining_Greek_Perispomeni = 834,
    U_Combining_Greek_Koronis = 835,
    U_Combining_Greek_Dialytika_Tonos = 836,
    U_Combining_Greek_Ypogegrammeni = 837,
    U_Combining_Bridge_Above = 838,
    U_Combining_Equals_Sign_Below = 839,
    U_Combining_Double_Vertical_Line_Below = 840,
    U_Combining_Left_Angle_Below = 841,
    U_Combining_Not_Tilde_Above = 842,
    U_Combining_Homothetic_Above = 843,
    U_Combining_Almost_Equal_To_Above = 844,
    U_Combining_Left_Right_Arrow_Below = 845,
    U_Combining_Upwards_Arrow_Below = 846,
    U_Combining_Grapheme_Joiner = 847,
    U_Combining_Right_Arrowhead_Above = 848,
    U_Combining_Left_Half_Ring_Above = 849,
    U_Combining_Fermata = 850,
    U_Combining_X_Below = 851,
    U_Combining_Left_Arrowhead_Below = 852,
    U_Combining_Right_Arrowhead_Below = 853,
    U_Combining_Right_Arrowhead_And_Up_Arrowhead_Below = 854,
    U_Combining_Right_Half_Ring_Above = 855,
    U_Combining_Dot_Above_Right = 856,
    U_Combining_Asterisk_Below = 857,
    U_Combining_Double_Ring_Below = 858,
    U_Combining_Zigzag_Above = 859,
    U_Combining_Double_Breve_Below = 860,
    U_Combining_Double_Breve = 861,
    U_Combining_Double_Macron = 862,
    U_Combining_Double_Macron_Below = 863,
    U_Combining_Double_Tilde = 864,
    U_Combining_Double_Inverted_Breve = 865,
    U_Combining_Double_Rightwards_Arrow_Below = 866,
    U_Combining_Latin_Small_Letter_A = 867,
    U_Combining_Latin_Small_Letter_E = 868,
    U_Combining_Latin_Small_Letter_I = 869,
    U_Combining_Latin_Small_Letter_O = 870,
    U_Combining_Latin_Small_Letter_U = 871,
    U_Combining_Latin_Small_Letter_C = 872,
    U_Combining_Latin_Small_Letter_D = 873,
    U_Combining_Latin_Small_Letter_H = 874,
    U_Combining_Latin_Small_Letter_M = 875,
    U_Combining_Latin_Small_Letter_R = 876,
    U_Combining_Latin_Small_Letter_T = 877,
    U_Combining_Latin_Small_Letter_V = 878,
    U_Combining_Latin_Small_Letter_X = 879,
    /**
     * Unicode Character 'LINE SEPARATOR' (U+2028)
     * http://www.fileformat.info/info/unicode/char/2028/index.htm
     */
    LINE_SEPARATOR = 8232,
    /**
     * Unicode Character 'PARAGRAPH SEPARATOR' (U+2029)
     * http://www.fileformat.info/info/unicode/char/2029/index.htm
     */
    PARAGRAPH_SEPARATOR = 8233,
    /**
     * Unicode Character 'NEXT LINE' (U+0085)
     * http://www.fileformat.info/info/unicode/char/0085/index.htm
     */
    NEXT_LINE = 133,
    U_CIRCUMFLEX = 94,
    U_GRAVE_ACCENT = 96,
    U_DIAERESIS = 168,
    U_MACRON = 175,
    U_ACUTE_ACCENT = 180,
    U_CEDILLA = 184,
    U_MODIFIER_LETTER_LEFT_ARROWHEAD = 706,
    U_MODIFIER_LETTER_RIGHT_ARROWHEAD = 707,
    U_MODIFIER_LETTER_UP_ARROWHEAD = 708,
    U_MODIFIER_LETTER_DOWN_ARROWHEAD = 709,
    U_MODIFIER_LETTER_CENTRED_RIGHT_HALF_RING = 722,
    U_MODIFIER_LETTER_CENTRED_LEFT_HALF_RING = 723,
    U_MODIFIER_LETTER_UP_TACK = 724,
    U_MODIFIER_LETTER_DOWN_TACK = 725,
    U_MODIFIER_LETTER_PLUS_SIGN = 726,
    U_MODIFIER_LETTER_MINUS_SIGN = 727,
    U_BREVE = 728,
    U_DOT_ABOVE = 729,
    U_RING_ABOVE = 730,
    U_OGONEK = 731,
    U_SMALL_TILDE = 732,
    U_DOUBLE_ACUTE_ACCENT = 733,
    U_MODIFIER_LETTER_RHOTIC_HOOK = 734,
    U_MODIFIER_LETTER_CROSS_ACCENT = 735,
    U_MODIFIER_LETTER_EXTRA_HIGH_TONE_BAR = 741,
    U_MODIFIER_LETTER_HIGH_TONE_BAR = 742,
    U_MODIFIER_LETTER_MID_TONE_BAR = 743,
    U_MODIFIER_LETTER_LOW_TONE_BAR = 744,
    U_MODIFIER_LETTER_EXTRA_LOW_TONE_BAR = 745,
    U_MODIFIER_LETTER_YIN_DEPARTING_TONE_MARK = 746,
    U_MODIFIER_LETTER_YANG_DEPARTING_TONE_MARK = 747,
    U_MODIFIER_LETTER_UNASPIRATED = 749,
    U_MODIFIER_LETTER_LOW_DOWN_ARROWHEAD = 751,
    U_MODIFIER_LETTER_LOW_UP_ARROWHEAD = 752,
    U_MODIFIER_LETTER_LOW_LEFT_ARROWHEAD = 753,
    U_MODIFIER_LETTER_LOW_RIGHT_ARROWHEAD = 754,
    U_MODIFIER_LETTER_LOW_RING = 755,
    U_MODIFIER_LETTER_MIDDLE_GRAVE_ACCENT = 756,
    U_MODIFIER_LETTER_MIDDLE_DOUBLE_GRAVE_ACCENT = 757,
    U_MODIFIER_LETTER_MIDDLE_DOUBLE_ACUTE_ACCENT = 758,
    U_MODIFIER_LETTER_LOW_TILDE = 759,
    U_MODIFIER_LETTER_RAISED_COLON = 760,
    U_MODIFIER_LETTER_BEGIN_HIGH_TONE = 761,
    U_MODIFIER_LETTER_END_HIGH_TONE = 762,
    U_MODIFIER_LETTER_BEGIN_LOW_TONE = 763,
    U_MODIFIER_LETTER_END_LOW_TONE = 764,
    U_MODIFIER_LETTER_SHELF = 765,
    U_MODIFIER_LETTER_OPEN_SHELF = 766,
    U_MODIFIER_LETTER_LOW_LEFT_ARROW = 767,
    U_GREEK_LOWER_NUMERAL_SIGN = 885,
    U_GREEK_TONOS = 900,
    U_GREEK_DIALYTIKA_TONOS = 901,
    U_GREEK_KORONIS = 8125,
    U_GREEK_PSILI = 8127,
    U_GREEK_PERISPOMENI = 8128,
    U_GREEK_DIALYTIKA_AND_PERISPOMENI = 8129,
    U_GREEK_PSILI_AND_VARIA = 8141,
    U_GREEK_PSILI_AND_OXIA = 8142,
    U_GREEK_PSILI_AND_PERISPOMENI = 8143,
    U_GREEK_DASIA_AND_VARIA = 8157,
    U_GREEK_DASIA_AND_OXIA = 8158,
    U_GREEK_DASIA_AND_PERISPOMENI = 8159,
    U_GREEK_DIALYTIKA_AND_VARIA = 8173,
    U_GREEK_DIALYTIKA_AND_OXIA = 8174,
    U_GREEK_VARIA = 8175,
    U_GREEK_OXIA = 8189,
    U_GREEK_DASIA = 8190,
    U_OVERLINE = 8254,
    /**
     * UTF-8 BOM
     * Unicode Character 'ZERO WIDTH NO-BREAK SPACE' (U+FEFF)
     * http://www.fileformat.info/info/unicode/char/feff/index.htm
     */
    UTF8_BOM = 65279
}
